package services

import (
	"errors"
	"time"

	"github.com/tangcaijun/copilot-demo-01/models"
)

// OrderValidator 订单验证服务
type OrderValidator struct {
	MaxOrderAmount float64
	MinOrderAmount float64
}

// NewOrderValidator 创建订单验证服务实例
func NewOrderValidator() *OrderValidator {
	return &OrderValidator{
		MaxOrderAmount: 100000.0, // 最大订单金额限制
		MinOrderAmount: 0.01,     // 最小订单金额限制
	}
}

// ValidateOrder 验证订单
func (v *OrderValidator) ValidateOrder(order *models.Order) error {
	// 基本验证
	if err := order.Validate(); err != nil {
		return err
	}

	// 验证订单金额范围
	if order.TotalAmount < v.MinOrderAmount {
		return errors.New("order amount is below minimum allowed amount")
	}
	if order.TotalAmount > v.MaxOrderAmount {
		return errors.New("order amount exceeds maximum allowed amount")
	}

	// 验证订单创建时间
	if order.CreatedAt.After(time.Now()) {
		return errors.New("order creation time cannot be in the future")
	}

	// 验证订单状态流转
	if err := v.validateOrderStatus(order); err != nil {
		return err
	}

	return nil
}

// validateOrderStatus 验证订单状态流转是否合法
func (v *OrderValidator) validateOrderStatus(order *models.Order) error {
	switch order.Status {
	case models.OrderStatusPending:
		// 新订单状态检查
		if !order.CreatedAt.Equal(order.LastUpdatedAt) {
			return errors.New("pending order should have same created and updated time")
		}
	case models.OrderStatusPaid:
		// 支付订单状态检查
		if order.TotalAmount <= 0 {
			return errors.New("paid order must have positive amount")
		}
	case models.OrderStatusCanceled:
		// 取消订单状态检查
		if order.Status == models.OrderStatusDelivered {
			return errors.New("delivered order cannot be canceled")
		}
	}

	return nil
}

// ValidateStatusTransition 验证订单状态转换是否合法
func (v *OrderValidator) ValidateStatusTransition(oldStatus, newStatus models.OrderStatus) error {
	// 定义合法的状态转换
	validTransitions := map[models.OrderStatus][]models.OrderStatus{
		models.OrderStatusPending: {
			models.OrderStatusPaid,
			models.OrderStatusCanceled,
		},
		models.OrderStatusPaid: {
			models.OrderStatusShipped,
			models.OrderStatusCanceled,
		},
		models.OrderStatusShipped: {
			models.OrderStatusDelivered,
		},
		models.OrderStatusDelivered: {}, // 终态，不能转换
		models.OrderStatusCanceled:  {}, // 终态，不能转换
	}

	// 检查状态转换是否合法
	allowedStatus, exists := validTransitions[oldStatus]
	if !exists {
		return errors.New("invalid current order status")
	}

	for _, status := range allowedStatus {
		if status == newStatus {
			return nil
		}
	}

	return errors.New("invalid status transition")
}
