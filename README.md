# 订单处理系统

这是一个使用Go语言开发的简单订单处理系统，用于管理和处理电子商务订单。系统支持订单验证、并发处理和折扣计算等功能。

## 功能特点

- 订单数据模型和验证
- 并发订单处理
- 自动折扣计算（满100元打9折）
- 详细的日志记录
- 订单状态管理

## 项目结构

```
.
├── main.go              # 程序入口点
├── models/              # 数据模型
│   ├── order.go         # 订单和订单项模型及验证
│   └── order_validator.go # 额外的订单验证逻辑
├── services/            # 业务逻辑
│   ├── order_service.go # 订单处理服务
│   └── order_validator.go # 订单验证服务
└── utils/               # 工具函数
    └── time_utils.go    # 时间处理工具
```

## 安装说明

### 前提条件

- Go 1.22.4 或更高版本

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/tangcaijun/copilot-demo-01.git
cd copilot-demo-01
```

2. 安装依赖

```bash
go mod tidy
```

## 使用示例

运行主程序：

```bash
go run main.go
```

这将创建并处理示例订单，输出处理结果到控制台和日志文件。

### 示例代码

创建和处理订单：

```go
// 创建订单服务
orderService := services.NewOrderService()

// 创建订单
order := models.Order{
    ID:          "1",
    UserID:      "101",
    TotalAmount: 99.9,
    Status:      models.OrderStatusPending,
    Items: []models.OrderItem{
        {
            ProductID:  "1001",
            Quantity:   1,
            UnitPrice:  99.9,
            TotalPrice: 99.9,
        },
    },
    CreatedAt:     time.Now(),
    LastUpdatedAt: time.Now(),
}

// 处理订单
if err := orderService.ProcessOrders([]models.Order{order}); err != nil {
    log.Printf("订单处理遇到错误: %v", err)
} else {
    log.Println("订单处理成功")
}
```

## 订单状态

系统支持以下订单状态：

- `pending`: 待处理
- `paid`: 已支付
- `shipped`: 已发货
- `delivered`: 已送达
- `canceled`: 已取消

## 许可证

[MIT](LICENSE)